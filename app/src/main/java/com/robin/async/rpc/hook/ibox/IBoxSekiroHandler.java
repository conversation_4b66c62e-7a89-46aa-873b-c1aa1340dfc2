package com.robin.async.rpc.hook.ibox;

import com.virjar.sekiro.business.api.interfaze.Action;
import com.virjar.sekiro.business.api.interfaze.AutoBind;
import com.virjar.sekiro.business.api.interfaze.RequestHandler;
import com.virjar.sekiro.business.api.interfaze.SekiroRequest;
import com.virjar.sekiro.business.api.interfaze.SekiroResponse;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;

@Action("iboxAppCall")
/* loaded from: classes3.dex */
public class IBoxSekiroHandler implements RequestHandler {

    @AutoBind
    private String base64Params;
    private Object targetObj;

    public IBoxSekiroHandler(Object targetObj) {
        this.targetObj = targetObj;
    }

    @Override // com.virjar.sekiro.business.api.interfaze.RequestHandler
    public void handleRequest(SekiroRequest sekiroRequest, SekiroResponse sekiroResponse) {
        String params = Base64.decodeStr(this.base64Params);
        XposedBridge.log("ibox param  => " + params);
        IBoxBody iBoxBody = (IBoxBody) JSONUtil.toBean(params, IBoxBody.class);
        Object o = XposedHelpers.callMethod(this.targetObj, "callContainer", new Object[]{"SYNC", iBoxBody.method, iBoxBody.url, iBoxBody.headers, iBoxBody.body});
        XposedBridge.log("ibox callRes  => " + JSONUtil.toJsonStr(o));
        sekiroResponse.success(JSONUtil.parseObj(o).getStr("respBody"));
    }
}