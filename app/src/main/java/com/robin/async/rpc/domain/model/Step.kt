package com.robin.async.rpc.domain.model

data class Step(
    val id: Long,
    val name: String,
    var intervalDelayMs: Long? = null, // 间隔延时时间（毫秒）
    var delayMs: Long? = null, // 步骤完成后的延迟时间（毫秒）
    val url: String, // 第三方接口地址
    val requestMethod: String, // 第三方接口请求方式
    val headers: Map<String, Any>, // 第三方接口请求头
    val configParam: StepConfigParam, // 第三方接口可配置请求参数
    val extendParamJson: String, // 第三方接口扩展请求参数Json
    val sort: Int,

    val completedKey: String, // 第三方响应判断步骤成功的Key
    val completedValue: String? = null, // 第三方响应判断步骤成功的Value
    val notCompletedValue: String? = null // 第三方响应判断步骤失败的Value
)

// TODO 此类定义应该由第三方下单接口决定
data class StepConfigParam(
    val standards: List<StandardEnum>? = null, // 规格-多选
    val deliveryMethod: DeliveryMethodEnum? = null, // 收货方式
    val oneCount: Int? = null, // 单盒下单数量
    val boxCount: Int? = null // 整端下单数量
)

enum class StandardEnum {
    ONE,
    BOX
}

enum class DeliveryMethodEnum {
    HOME,
    STORE
}