package com.robin.async.rpc.api

import com.robin.async.rpc.constant.ApiConstant
import com.robin.async.rpc.domain.model.Account
import com.robin.async.rpc.domain.model.BackendResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST

interface AccountApi {

    @GET("${ApiConstant.BASE_APP_URL}/{mobile}")
    fun findAccount(mobile: String): Response<BackendResponse>

    @POST("${ApiConstant.BASE_APP_URL}/event")
    fun accountEvent(@Body account: Account): Response<BackendResponse>

    @POST("${ApiConstant.BASE_APP_URL}/gen-cloud-param")
    fun generateCloudParams(@Body body: String, androidId: String): Response<BackendResponse>

}