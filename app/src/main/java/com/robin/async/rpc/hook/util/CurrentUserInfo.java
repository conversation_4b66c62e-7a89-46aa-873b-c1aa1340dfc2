package com.robin.async.rpc.hook.util;

import android.annotation.SuppressLint;
import android.content.Context;

/* loaded from: classes6.dex */
public class CurrentUserInfo {
    public static String androidId;
    public static String appId;
    @SuppressLint("StaticFieldLeak")
    public static Context context;
    public static Long invokeTime = 0L;
    public static String mobile;
    public static String openId;
    public static String tongdunTokenId;
    public static String ua;

    public static String getMobile() {
        return mobile;
    }

    public static void setMobile(String mobile2) {
        mobile = mobile2;
    }
}