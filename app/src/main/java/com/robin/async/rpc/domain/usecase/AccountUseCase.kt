package com.robin.async.rpc.domain.usecase

import cn.hutool.json.JSONObject
import com.robin.async.rpc.api.AccountApi
import com.robin.async.rpc.domain.model.Account
import javax.inject.Inject

class AccountUseCase @Inject constructor(
    private val accountApi: AccountApi
) {
    // 初始化上传认证信息
    fun accountEvent(jsonObject: JSONObject) {
        val mobile = jsonObject.getJSONObject("mobile") as String
        val account = (accountApi.findAccount(mobile).body()?.data as Account?) ?: Account()
        account.apply {
            this.event = jsonObject.getJSONObject("event") as String
            this.mobile = jsonObject.getJSONObject("mobile") as String
            this.appId = jsonObject.getJSONObject("appId") as String
            this.androidId = jsonObject.getJSONObject("androidId") as String
            this.ua = jsonObject.getJSONObject("ua") as String
            this.openId = jsonObject.getJSONObject("openId") as String
            this.tongdunTokenId = jsonObject.getJSONObject("tongdunTokenId") as String
            this.token = jsonObject.getJSONObject("token") as String
        }
        accountApi.accountEvent(account)
    }

    fun generateCloudParams(body: String, androidId: String) =
        accountApi.generateCloudParams(body, androidId)

}