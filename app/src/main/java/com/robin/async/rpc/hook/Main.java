package com.robin.async.rpc.hook;

import android.app.Application;
import android.content.Context;
import android.os.Looper;
import android.provider.Settings;
import android.widget.Toast;
import androidx.core.app.NotificationCompat;
import com.robin.async.rpc.domain.usecase.AccountUseCase;
import com.robin.async.rpc.hook.util.CurrentUserInfo;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage;

/* loaded from: classes4.dex */
public class Main implements IXposedHookLoadPackage {
    private Object jniObject;
    private Long lastInvokeTime = 0L;
    private String lastGetServiceInfoData = "";

    public AccountUseCase accountUseCase;

    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam loadPackageParam) throws Throwable {
        XposedBridge.log("has Hooked!");
        String processName = loadPackageParam.packageName;
        XposedBridge.log("inner  => " + processName);
        System.out.println("now processName:" + processName);
        if (processName.equals("com.tencent.mm")) {
            XposedHelpers.findAndHookMethod(loadPackageParam.classLoader.loadClass("com.tencent.mm.appbrand.commonjni.AppBrandJsBridgeBinding"), "nativeInvokeCallbackHandler", new Object[]{Long.TYPE, Integer.TYPE, String.class, String.class, new XC_MethodHook() { // from class: rpc.robin.Main.1
                protected void beforeHookedMethod(XC_MethodHook.MethodHookParam param) throws Throwable {
                    Main.this.resolveMsg(param.args[2].toString(), param.args);
                    super.beforeHookedMethod(param);
                }

                protected void afterHookedMethod(XC_MethodHook.MethodHookParam param) throws Throwable {
                    super.afterHookedMethod(param);
                }
            }});
            XposedHelpers.findAndHookMethod("com.tencent.mm.appbrand.commonjni.AppBrandCommonBindingJni", loadPackageParam.classLoader, "nativeInvokeHandler", new Object[]{String.class, String.class, String.class, Integer.TYPE, Boolean.TYPE, Integer.TYPE, Integer.TYPE, new XC_MethodHook() { // from class: rpc.robin.Main.2
                protected void beforeHookedMethod(XC_MethodHook.MethodHookParam param) throws Throwable {
                    String paramStr = JSONUtil.toJsonStr(param.args);
                    if (!paramStr.contains("getStorageSync")) {
                        XposedBridge.log("获取到了jniObject,params:" + paramStr);
                    }
                    if (paramStr.contains("tcbapi_call_gateway") && paramStr.contains("authorization") && paramStr.contains("popbox.paquapp.com")) {
                        Main.this.resolveMsg(param.args[1].toString(), null);
                    }
                    if (paramStr.contains("wx9627eb7f4b1c69d5")) {
                        CurrentUserInfo.appId = "wx9627eb7f4b1c69d5";
                    } else if (paramStr.contains("wx15f182efdb0ba66c")) {
                        CurrentUserInfo.appId = "wx15f182efdb0ba66c";
                    }
                    if (paramStr.contains("tcbapi_get_service_info") && paramStr.contains("operateWXData") && !paramStr.contains("4ce6552b432463ea4b3a990d15f97c0c51d48caf6c331f60670593cd3c4fae1d")) {
                        Main.this.lastGetServiceInfoData = param.args[1].toString();
                        XposedBridge.log("获取到了上次调用tcbapi_get_service_info参数,params:" + Main.this.lastGetServiceInfoData);
                    }
                    Main.this.jniObject = param.thisObject;
                    super.beforeHookedMethod(param);
                }

                protected void afterHookedMethod(XC_MethodHook.MethodHookParam param) throws Throwable {
                    XposedBridge.log("获取到了jniObject,result:");
                    XposedBridge.log("获取到了jniObject,result:" + param.getResult());
                    super.afterHookedMethod(param);
                }
            }});
        }
        if (processName.equals("com.tencent.mm")) {
            Class ActivityThread = XposedHelpers.findClass("android.app.ActivityThread", loadPackageParam.classLoader);
            XposedBridge.hookAllMethods(ActivityThread, "performLaunchActivity", new XC_MethodHook() { // from class: rpc.robin.Main.3
                protected void afterHookedMethod(XC_MethodHook.MethodHookParam param) throws Throwable {
                    super.afterHookedMethod(param);
                    Object mInitialApplication = (Application) XposedHelpers.getObjectField(param.thisObject, "mInitialApplication");
                    CurrentUserInfo.context = (Context) mInitialApplication;
                    if (mInitialApplication != null) {
                        String androidId = Main.this.getAndroidId((Application) mInitialApplication);
                        XposedBridge.log("Android ID: " + androidId);
                        CurrentUserInfo.androidId = androidId;
                    } else {
                        XposedBridge.log("Failed to get app context.");
                    }
                    ClassLoader finalCL = (ClassLoader) XposedHelpers.callMethod(mInitialApplication, "getClassLoader", new Object[0]);
                    XposedBridge.log("微信 found classload is => " + finalCL.toString());
                    Class BabyMain = (Class) XposedHelpers.callMethod(finalCL, "findClass", new Object[]{"com.tencent.mm.appbrand.commonjni.AppBrandJsBridgeBinding"});
                    XposedBridge.log("微信 found final class is => " + BabyMain.getName().toString());
                    XposedHelpers.findAndHookMethod(BabyMain, "nativeInvokeCallbackHandler", new Object[]{Long.TYPE, Integer.TYPE, String.class, String.class, new XC_MethodHook() { // from class: rpc.robin.Main.3.1
                        protected void beforeHookedMethod(XC_MethodHook.MethodHookParam param2) throws Throwable {
                            Main.this.resolveMsg(param2.args[2].toString(), param2.args);
                            super.beforeHookedMethod(param2);
                        }

                        protected void afterHookedMethod(XC_MethodHook.MethodHookParam param2) throws Throwable {
                            XposedBridge.log("获取到了jniObject,result:params:" + JSONUtil.toJsonStr(param2.args));
                            XposedBridge.log("获取到了jniObject,result:" + param2.getResult());
                            super.afterHookedMethod(param2);
                        }
                    }});
                    XposedHelpers.findAndHookMethod("com.tencent.mm.appbrand.commonjni.AppBrandCommonBindingJni", finalCL, "nativeInvokeHandler", new Object[]{String.class, String.class, String.class, Integer.TYPE, Boolean.TYPE, Integer.TYPE, Integer.TYPE, new XC_MethodHook() { // from class: rpc.robin.Main.3.2
                        protected void beforeHookedMethod(XC_MethodHook.MethodHookParam param2) throws Throwable {
                            String paramStr = JSONUtil.toJsonStr(param2.args);
                            if (!paramStr.contains("getStorageSync")) {
                                XposedBridge.log("获取到了jniObject,params:" + paramStr);
                            }
                            if (paramStr.contains("tcbapi_call_gateway") && paramStr.contains("authorization") && paramStr.contains("popbox.paquapp.com")) {
                                Main.this.resolveMsg(param2.args[1].toString(), null);
                            }
                            if (paramStr.contains("wx9627eb7f4b1c69d5")) {
                                CurrentUserInfo.appId = "wx9627eb7f4b1c69d5";
                            } else if (paramStr.contains("wx15f182efdb0ba66c")) {
                                CurrentUserInfo.appId = "wx15f182efdb0ba66c";
                            }
                            if (paramStr.contains("tcbapi_get_service_info") && paramStr.contains("operateWXData") && !paramStr.contains("4ce6552b432463ea4b3a990d15f97c0c51d48caf6c331f60670593cd3c4fae1d")) {
                                Main.this.lastGetServiceInfoData = param2.args[1].toString();
                                XposedBridge.log("获取到了上次调用tcbapi_get_service_info参数,params:" + Main.this.lastGetServiceInfoData);
                            }
                            Main.this.jniObject = param2.thisObject;
                            super.beforeHookedMethod(param2);
                        }

                        protected void afterHookedMethod(XC_MethodHook.MethodHookParam param2) throws Throwable {
                            String paramStr = JSONUtil.toJsonStr(param2.args);
                            if (paramStr.contains("_fmBlackbox")) {
                                Object result = param2.getResult();
                                XposedBridge.log("获取到了blackBox,result:" + result);
                                JSONObject jsonObj = JSONUtil.parseObj(result);
                                CurrentUserInfo.tongdunTokenId = jsonObj.getStr("data").split("\\^\\^")[1];
                            }
                            super.afterHookedMethod(param2);
                        }
                    }});
                }
            });
        }
    }

    public void resolveMsg(String msg, Object[] args) throws HttpException, JSONException {
        String appId;
        String str;
        String str2;
        if (msg.contains("wx9627eb7f4b1c69d5")) {
            CurrentUserInfo.appId = "wx9627eb7f4b1c69d5";
        } else if (msg.contains("wx15f182efdb0ba66c")) {
            CurrentUserInfo.appId = "wx15f182efdb0ba66c";
        }
        String appId2 = CurrentUserInfo.appId;
        if (!StrUtil.isEmpty(appId2)) {
            appId = appId2;
        } else {
            appId = "wx9627eb7f4b1c69d5";
        }
        XposedBridge.log("收到响应4" + msg);
        StringBuilder sb = new StringBuilder();
        sb.append("当前的手机号为:");
        sb.append(CurrentUserInfo.getMobile());
        XposedBridge.log(sb.toString());
        String str3 = "ua";
        if (msg.contains("kx") && msg.contains("token")) {
            if (msg.contains("rid") && !msg.contains("encrypt_key")) {
                if (System.currentTimeMillis() - this.lastInvokeTime.longValue() > 10000 && StrUtil.isNotBlank(this.lastGetServiceInfoData)) {
                    this.lastInvokeTime = Long.valueOf(System.currentTimeMillis());
                    final JSONObject invokeObj = JSONUtil.parseObj(this.lastGetServiceInfoData);
                    JSONObject cloudParams = generateCloudParams(invokeObj.getJSONObject("data").getJSONObject("data").toString(), CurrentUserInfo.androidId);
                    invokeObj.getJSONObject("data").getJSONObject("data").putOpt("cli_req_id", cloudParams.getStr("clientReqId"));
                    invokeObj.getJSONObject("data").getJSONObject("data").getJSONObject("qbase_options").putOpt("rid", cloudParams.getStr("rid"));
                    invokeObj.getJSONObject("data").getJSONObject("data").getJSONObject("qbase_options").putOpt("kx", cloudParams.getStr("clientPublicKey"));
                    ThreadUtil.execute(() -> {
                        ThreadUtil.sleep(3000L);
                        CurrentUserInfo.invokeTime = Long.valueOf(System.currentTimeMillis());
                        XposedHelpers.callMethod(Main.this.jniObject, "nativeInvokeHandler", new Object[]{"operateWXData", invokeObj.toString(), StrPool.EMPTY_JSON, Integer.valueOf(RandomUtil.randomInt(1, 100)), true, 0, 0});
                        XposedBridge.log("主动调用方法成功");
                    });
                }
                String data = JSONUtil.parseObj(msg).getStr("data");
                String tokenData = JSONUtil.parseObj(data).getStr("data");
                JSONObject tokenObj = JSONUtil.parseObj(tokenData);
                JSONObject postData = new JSONObject();
                postData.putOpt(NotificationCompat.CATEGORY_EVENT, "token");
                postData.putOpt("data", tokenObj.toString());
                postData.putOpt("mobile", CurrentUserInfo.getMobile());
                postData.putOpt("appId", appId);
                postData.putOpt("androidId", CurrentUserInfo.androidId);
                postData.putOpt("ua", CurrentUserInfo.ua);
                postData.putOpt("openId", CurrentUserInfo.openId);
                postData.putOpt("tongdunTokenId", CurrentUserInfo.tongdunTokenId);
                accountUseCase.accountEvent(postData);
                XposedBridge.log("发送event body:" + postData.toString());
                if (System.currentTimeMillis() - CurrentUserInfo.invokeTime.longValue() < 2000) {
                    ThreadUtil.execute(() -> {
                        accountUseCase.accountEvent(postData);
                        Looper.prepare();
                        Toast.makeText(CurrentUserInfo.context, "token上传成功", Toast.LENGTH_LONG).show();
                        Looper.loop();
                    });
                    return;
                }
                return;
            }
            str = "tongdunTokenId";
            str2 = "openId";
            str3 = "ua";
        } else {
            str = "tongdunTokenId";
            str2 = "openId";
        }
        if (msg.contains("encrypt_key") && msg.contains("iv")) {
            String data2 = JSONUtil.parseObj(msg).getStr("data");
            String encryptKeyData = JSONUtil.parseObj(data2).getStr("data");
            JSONObject postData2 = new JSONObject();
            postData2.putOpt(NotificationCompat.CATEGORY_EVENT, "encryptKey");
            postData2.putOpt("data", encryptKeyData);
            postData2.putOpt("mobile", CurrentUserInfo.mobile);
            postData2.putOpt("appId", appId);
            postData2.putOpt("androidId", CurrentUserInfo.androidId);
            postData2.putOpt(str3, CurrentUserInfo.ua);
            postData2.putOpt(str2, CurrentUserInfo.openId);
            XposedBridge.log("发送加密key body:" + postData2.toString());
            ThreadUtil.execute(() -> accountUseCase.accountEvent(postData2));
            return;
        }
        if (msg.contains("webapi_getwxauserallphone")) {
            String resp_data = JSONUtil.parseObj(JSONUtil.parseObj(msg).getStr("fetchedData")).getJSONArray("resp_list").getJSONObject(0).getStr("resp_data");
            String realData = JSONUtil.parseObj(resp_data).getStr("data");
            String mobile = JSONUtil.parseObj(realData).getJSONObject("wx_phone").getStr("mobile");
            XposedBridge.log("成功获取到手机号:" + mobile);
            CurrentUserInfo.setMobile(mobile);
            JSONObject postData3 = new JSONObject();
            postData3.putOpt(NotificationCompat.CATEGORY_EVENT, "mobile");
            postData3.putOpt("mobile", CurrentUserInfo.getMobile());
            postData3.putOpt("appId", appId);
            postData3.putOpt("androidId", CurrentUserInfo.androidId);
            postData3.putOpt(str3, CurrentUserInfo.ua);
            ThreadUtil.execute(() -> accountUseCase.accountEvent(postData3));
            return;
        }
        if (msg.contains("mobile") && msg.contains("wx_phone")) {
            String innerData = JSONUtil.parseObj(JSONUtil.parseObj(msg).getStr("data")).getStr("data");
            String mobileData = JSONUtil.parseObj(innerData).getStr("data");
            JSONObject data3 = JSONUtil.parseObj(mobileData);
            String mobile2 = data3.getJSONObject("wx_phone").getStr("mobile");
            XposedBridge.log("成功获取到手机号:" + mobile2);
            CurrentUserInfo.setMobile(mobile2);
            JSONObject postData4 = new JSONObject();
            postData4.putOpt(NotificationCompat.CATEGORY_EVENT, "mobile");
            postData4.putOpt("mobile", CurrentUserInfo.getMobile());
            postData4.putOpt("appId", appId);
            postData4.putOpt("androidId", CurrentUserInfo.androidId);
            postData4.putOpt(str3, CurrentUserInfo.ua);
            ThreadUtil.execute(() -> accountUseCase.accountEvent(postData4));
            return;
        }
        if (msg.contains("mobile") && msg.contains("custom_phone_list")) {
            String innerData2 = JSONUtil.parseObj(JSONUtil.parseObj(msg).getStr("data")).getStr("data");
            String mobileData2 = JSONUtil.parseObj(innerData2).getStr("data");
            JSONObject data4 = JSONUtil.parseObj(mobileData2);
            String mobile3 = data4.getJSONArray("custom_phone_list").getJSONObject(0).getStr("mobile");
            XposedBridge.log("成功获取到手机号:" + mobile3);
            CurrentUserInfo.setMobile(mobile3);
            JSONObject postData5 = new JSONObject();
            postData5.putOpt(NotificationCompat.CATEGORY_EVENT, "mobile");
            postData5.putOpt("mobile", CurrentUserInfo.getMobile());
            postData5.putOpt("appId", appId);
            postData5.putOpt("androidId", CurrentUserInfo.androidId);
            postData5.putOpt(str3, CurrentUserInfo.ua);
            ThreadUtil.execute(() -> accountUseCase.accountEvent(postData5));
            return;
        }
        if (msg.contains("imei") && msg.contains(str3)) {
            String ua = JSONUtil.parseObj(msg).getStr(str3);
            CurrentUserInfo.ua = ua;
            return;
        }
        if (msg.contains("OpenId") && msg.contains("UserUin")) {
            String innerData3 = JSONUtil.parseObj(JSONUtil.parseObj(msg).getStr("data")).getStr("data");
            CurrentUserInfo.openId = JSONUtil.parseObj(innerData3).getStr("OpenId");
            JSONObject postData6 = new JSONObject();
            postData6.putOpt(NotificationCompat.CATEGORY_EVENT, str2);
            postData6.putOpt("mobile", CurrentUserInfo.getMobile());
            postData6.putOpt("appId", appId);
            postData6.putOpt("androidId", CurrentUserInfo.androidId);
            postData6.putOpt(str3, CurrentUserInfo.ua);
            postData6.putOpt(str2, CurrentUserInfo.openId);
            XposedBridge.log("成功获取到openId:" + CurrentUserInfo.openId);
            ThreadUtil.execute(() -> accountUseCase.accountEvent(postData6));
            return;
        }
        if (msg.contains("USER_INFO") && msg.contains("IDENTIFIERUSER")) {
            String fetchedData = JSONUtil.parseObj(msg).getStr("data");
            CurrentUserInfo.openId = JSONUtil.parseObj(fetchedData).getJSONObject("USER_INFO").getStr("open_id");
            String app_id = JSONUtil.parseObj(fetchedData).getJSONObject("USER_INFO").getStr("app_id");
            JSONObject postData7 = new JSONObject();
            postData7.putOpt(NotificationCompat.CATEGORY_EVENT, str2);
            postData7.putOpt("mobile", CurrentUserInfo.getMobile());
            postData7.putOpt("appId", app_id);
            postData7.putOpt("androidId", CurrentUserInfo.androidId);
            postData7.putOpt(str3, CurrentUserInfo.ua);
            postData7.putOpt(str2, CurrentUserInfo.openId);
            postData7.putOpt(str, CurrentUserInfo.tongdunTokenId);
            XposedBridge.log("成功获取到openId:" + CurrentUserInfo.openId);
            ThreadUtil.execute(() -> accountUseCase.accountEvent(postData7));
            return;
        }
        if (msg.contains("user_id") && msg.contains("client_key") && msg.contains("token")) {
            String fetchedData2 = JSONUtil.parseObj(msg).getStr("data");
            JSONObject postData8 = new JSONObject();
            postData8.putOpt(NotificationCompat.CATEGORY_EVENT, "userinfo");
            postData8.putOpt("mobile", CurrentUserInfo.getMobile());
            postData8.putOpt("androidId", CurrentUserInfo.androidId);
            postData8.putOpt(str3, CurrentUserInfo.ua);
            postData8.putOpt(str2, CurrentUserInfo.openId);
            postData8.putOpt("data", fetchedData2);
            postData8.putOpt("appId", appId);
            XposedBridge.log("成功获取到抽盒机token:" + fetchedData2);
            ThreadUtil.execute(() -> accountUseCase.accountEvent(postData8));
            return;
        }
        if (msg.contains("tcbapi_call_gateway") && msg.contains("authorization") && msg.contains("popbox.paquapp.com")) {
            String fetchedData3 = JSONUtil.parseObj(msg).getJSONObject("data").getJSONObject("data").getStr("qbase_req");
            JSONArray headers = JSONUtil.parseObj(fetchedData3).getJSONArray("headers");
            JSONObject postUserData = new JSONObject();
            for (int i = 0; i < headers.size(); i++) {
                JSONObject jsonObject = headers.getJSONObject(Integer.valueOf(i));
                postUserData.putOpt(jsonObject.getStr("k"), jsonObject.getStr("v"));
            }
            JSONObject postData9 = new JSONObject();
            postData9.putOpt(NotificationCompat.CATEGORY_EVENT, "userinfoByCallGateway");
            postData9.putOpt("mobile", CurrentUserInfo.getMobile());
            postData9.putOpt("androidId", CurrentUserInfo.androidId);
            postData9.putOpt(str3, CurrentUserInfo.ua);
            postData9.putOpt(str2, CurrentUserInfo.openId);
            postData9.putOpt("appId", appId);
            postData9.putOpt("data", postUserData.toString());
            XposedBridge.log("成功获取到抽盒机token:" + fetchedData3);
            ThreadUtil.execute(() -> accountUseCase.accountEvent(postData9));
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public String getAndroidId(Context context) {
        return Settings.Secure.getString(context.getContentResolver(), "android_id");
    }

    public JSONObject generateCloudParams(String body, String androidId) throws HttpException {
        XposedBridge.log(androidId + "生成云函数参数:" + body);

        String res = (String) accountUseCase.generateCloudParams(body, androidId).body().getData();
        XposedBridge.log("生成云函数参数返回:" + res);
        return JSONUtil.parseObj(res).getJSONObject("data");
    }
}