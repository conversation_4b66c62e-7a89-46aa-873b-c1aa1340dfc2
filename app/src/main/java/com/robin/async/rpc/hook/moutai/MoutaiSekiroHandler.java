package com.robin.async.rpc.hook.moutai;

import com.virjar.sekiro.business.api.interfaze.Action;
import com.virjar.sekiro.business.api.interfaze.AutoBind;
import com.virjar.sekiro.business.api.interfaze.RequestHandler;
import com.virjar.sekiro.business.api.interfaze.SekiroRequest;
import com.virjar.sekiro.business.api.interfaze.SekiroResponse;

import de.robv.android.xposed.XposedHelpers;

@Action("moutai_mt-v")
/* loaded from: classes3.dex */
public class MoutaiSekiroHandler implements RequestHandler {

    @AutoBind
    private String deviceId;
    private Object targetObj;

    @AutoBind
    private String timeStamp;

    public MoutaiSekiroHandler(Object targetObj) {
        this.targetObj = targetObj;
    }

    @Override // com.virjar.sekiro.business.api.interfaze.RequestHandler
    public void handleRequest(SekiroRequest sekiroRequest, SekiroResponse sekiroResponse) {
        String mtV = (String) XposedHelpers.callMethod(this.targetObj, "mixK", new Object[]{this.timeStamp, this.deviceId, ""});
        sekiroResponse.success(mtV);
    }
}